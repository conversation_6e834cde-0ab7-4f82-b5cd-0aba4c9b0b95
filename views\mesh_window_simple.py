"""
简化的网格窗口模块

此模块定义了简化版的网格管理窗口类，负责：
1. 显示网格管理界面
2. 处理网格参数的增删改查操作
3. 提供基本的网格生成和模态分析功能

作者: AI Assistant
日期: 2025-07-25
"""

import logging
import json
from datetime import datetime
from typing import Optional, List
from PySide6.QtCore import Qt
from PySide6.QtGui import QStandardItemModel, QStandardItem, QColor
from PySide6.QtWidgets import (QMessageBox, QTableWidgetItem, QHeaderView,
                               QPushButton, QHBoxLayout, QWidget,
                               QFileDialog, QListWidgetItem, QAbstractItemView,
                               QMenu, QApplication)

from ui import ui_mesh_new
from .base_window import BaseWindow
from .mesh_parameter_dialog import MeshParameterDialog
from core.mesh_manager import MeshManager, MeshParameter, MeshStatus, ElementType

# 获取模块日志记录器
logger = logging.getLogger(__name__)


class MeshWindow(BaseWindow):
    """简化的网格管理窗口类"""

    def __init__(self, window_manager):
        super().__init__(window_manager)

        # 设置原始的UI
        self.ui = ui_mesh_new.Ui_MainWindow()
        self.ui.setupUi(self)
        self.setWindowTitle("网格无关性验证系统")

        # 初始化网格管理器
        self.mesh_manager = MeshManager()

        # 设置窗口样式
        self._setup_window_style()

        # 初始化UI组件
        self._setup_ui_components()

        # 应用按钮动画效果（必须在信号槽连接之前）
        self.setup_animated_buttons()

        # 连接信号槽（必须在按钮替换之后）
        self._connect_signals()

        # 加载配置数据
        self._load_configuration()

        logger.info("简化网格管理窗口初始化完成")

    def _setup_window_style(self):
        """设置窗口样式"""
        self.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 5px;
                margin-top: 1ex;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            QPushButton {
                background-color: #f0f0f0;
                border: 1px solid #cccccc;
                border-radius: 4px;
                padding: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #e0e0e0;
            }
            QPushButton:pressed {
                background-color: #d0d0d0;
            }
        """)

    def _setup_ui_components(self):
        """初始化UI组件"""
        # 设置网格参数表格
        self._setup_mesh_table()

        # 设置批量操作列表
        self._setup_batch_operation_list()

        # 设置网格对比表格
        self._setup_comparison_table()

        # 设置结果对比列表
        self._setup_result_comparison_list()

        # 设置标签页
        self._setup_tab_widget()

        logger.debug("UI组件初始化完成")

    def _setup_mesh_table(self):
        """设置网格参数表格"""
        table = self.ui.tableWidget_mesh_params

        # 设置表格属性
        table.setAlternatingRowColors(True)
        table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        table.setSortingEnabled(True)

        # 设置列宽模式
        header = table.horizontalHeader()
        header.setStretchLastSection(False)  # 不让最后一列自动拉伸

        # 设置各列的宽度策略
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)  # 名称列
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)  # 尺寸列
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)  # 单元类型列
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)  # 状态列
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.Stretch)          # 创建时间列
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.ResizeToContents)  # 操作列

        # 设置行高
        table.verticalHeader().setDefaultSectionSize(35)
        table.verticalHeader().setVisible(False)

        logger.debug("网格参数表格设置完成")

    def _setup_batch_operation_list(self):
        """设置批量操作列表"""
        try:
            # 设置选中网格列表
            list_widget = self.ui.listWidget_selected_meshes
            list_widget.setSelectionMode(QAbstractItemView.SelectionMode.MultiSelection)
            list_widget.clear()

            # 设置对比网格列表
            comparison_list = self.ui.listWidget_comparison_meshes
            comparison_list.setSelectionMode(QAbstractItemView.SelectionMode.MultiSelection)
            comparison_list.clear()

            logger.debug("批量操作列表设置完成")
        except Exception as e:
            logger.error(f"设置批量操作列表失败: {str(e)}", exc_info=True)

    def _setup_comparison_table(self):
        """设置网格对比表格"""
        try:
            # 设置网格生成表格
            if hasattr(self.ui, 'tableWidget_generation_status'):
                table = self.ui.tableWidget_generation_status
                table.setAlternatingRowColors(True)
                table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)

            logger.debug("网格对比表格设置完成")
        except Exception as e:
            logger.error(f"设置网格对比表格失败: {str(e)}", exc_info=True)

    def _setup_result_comparison_list(self):
        """设置结果对比列表"""
        try:
            # 初始化显示选项
            if hasattr(self.ui, 'checkBox_show_frequency'):
                self.ui.checkBox_show_frequency.setChecked(True)
            if hasattr(self.ui, 'checkBox_show_convergence'):
                self.ui.checkBox_show_convergence.setChecked(True)
            if hasattr(self.ui, 'checkBox_show_grid'):
                self.ui.checkBox_show_grid.setChecked(False)

            logger.debug("结果对比列表设置完成")
        except Exception as e:
            logger.error(f"设置结果对比列表失败: {str(e)}", exc_info=True)

    def _setup_tab_widget(self):
        """设置标签页"""
        try:
            # 设置默认标签页
            self.ui.tabWidget_main.setCurrentIndex(0)

            # 初始化模态参数
            if hasattr(self.ui, 'spinBox_modal_count'):
                self.ui.spinBox_modal_count.setValue(10)
            if hasattr(self.ui, 'doubleSpinBox_freq_min'):
                self.ui.doubleSpinBox_freq_min.setValue(0.0)
            if hasattr(self.ui, 'doubleSpinBox_freq_max'):
                self.ui.doubleSpinBox_freq_max.setValue(1000.0)
            if hasattr(self.ui, 'checkBox_limit_freq'):
                self.ui.checkBox_limit_freq.setChecked(True)

            logger.debug("标签页设置完成")
        except Exception as e:
            logger.error(f"设置标签页失败: {str(e)}", exc_info=True)

    def _setup_mesh_preview(self):
        """设置网格预览下拉框"""
        combo = self.ui.comboBox_mesh_select
        combo.clear()
        combo.addItem("请选择网格", None)

        # 设置样式
        combo.setStyleSheet("""
            QComboBox {
                border: 1px solid #cccccc;
                border-radius: 4px;
                padding: 5px;
                background-color: white;
            }
            QComboBox::drop-down {
                border: none;
            }
            QComboBox::down-arrow {
                image: url(down_arrow.png);
                width: 12px;
                height: 12px;
            }
        """)

        logger.debug("网格预览下拉框设置完成")

    def _connect_signals(self):
        """连接信号槽"""
        # 网格管理标签页信号
        self.ui.btn_add_mesh.clicked.connect(self._on_add_mesh)
        self.ui.btn_import_mesh.clicked.connect(self._on_import_mesh)
        self.ui.btn_export_mesh.clicked.connect(self._on_export_mesh)

        # 表格信号
        self.ui.tableWidget_mesh_params.itemSelectionChanged.connect(self._on_mesh_selection_changed)
        self.ui.tableWidget_mesh_params.cellDoubleClicked.connect(self._on_mesh_double_clicked)

        # 批量操作信号
        self.ui.btn_select_all_meshes.clicked.connect(self._on_select_all_meshes)
        self.ui.btn_select_none_meshes.clicked.connect(self._on_select_none_meshes)
        self.ui.btn_select_inverse_meshes.clicked.connect(self._on_select_inverse_meshes)
        self.ui.btn_apply_filter.clicked.connect(self._on_apply_filter)
        self.ui.btn_batch_generate.clicked.connect(self._on_batch_generate)
        self.ui.btn_stop_generation.clicked.connect(self._on_stop_generation)

        # 模态分析信号
        self.ui.btn_single_modal.clicked.connect(self._on_single_modal)
        self.ui.btn_start_modal_calculation.clicked.connect(self._on_start_modal_calculation)
        self.ui.checkBox_limit_freq.toggled.connect(self._on_freq_limit_toggled)
        self.ui.spinBox_modal_count.valueChanged.connect(self._on_modal_params_changed)
        self.ui.doubleSpinBox_freq_min.valueChanged.connect(self._on_modal_params_changed)
        self.ui.doubleSpinBox_freq_max.valueChanged.connect(self._on_modal_params_changed)

        # 模态网格选择信号
        self.ui.btn_use_selected_meshes.clicked.connect(self._on_use_selected_meshes)
        self.ui.btn_use_recommended_mesh.clicked.connect(self._on_use_recommended_mesh)
        self.ui.btn_select_all_for_modal.clicked.connect(self._on_select_all_for_modal)

        # 批量计算信号
        self.ui.btn_batch_calculate.clicked.connect(self._on_batch_calculate)
        self.ui.btn_pause_calculation.clicked.connect(self._on_pause_calculation)
        self.ui.btn_stop_calculation.clicked.connect(self._on_stop_calculation)

        # 最优推荐信号
        self.ui.btn_analyze_convergence.clicked.connect(self._on_analyze_convergence)
        self.ui.btn_recommend_mesh.clicked.connect(self._on_recommend_mesh)
        self.ui.btn_confirm_recommendation.clicked.connect(self._on_confirm_recommendation)

        # 结果对比信号
        self.ui.btn_export_results.clicked.connect(self._on_export_results)
        self.ui.checkBox_show_frequency.toggled.connect(self._on_display_option_changed)
        self.ui.checkBox_show_convergence.toggled.connect(self._on_display_option_changed)
        self.ui.checkBox_show_grid.toggled.connect(self._on_display_option_changed)

        # 底部导航按钮信号
        self.ui.btn_generate_mesh.clicked.connect(self._on_generate_mesh_nav)
        self.ui.btn_view_results.clicked.connect(self._on_view_results)
        self.ui.btn_previous.clicked.connect(self._on_previous)
        self.ui.btn_next.clicked.connect(self._on_next)
        self.ui.btn_main_menu.clicked.connect(self._on_main_menu)

        # 标签页切换信号
        self.ui.tabWidget_main.currentChanged.connect(self._on_tab_changed)

        # 网格管理器信号
        self.mesh_manager.signals.mesh_added.connect(self._on_mesh_added)
        self.mesh_manager.signals.mesh_removed.connect(self._on_mesh_removed)
        self.mesh_manager.signals.mesh_updated.connect(self._on_mesh_updated)
        self.mesh_manager.signals.status_changed.connect(self._on_mesh_status_changed)
        self.mesh_manager.signals.current_mesh_changed.connect(self._on_current_mesh_changed)
        self.mesh_manager.signals.error_occurred.connect(self._on_mesh_error)

        logger.debug("信号槽连接完成")

    def _load_configuration(self):
        """加载配置数据"""
        try:
            from core.config_manager import ConfigManager
            config_manager = ConfigManager()

            # 加载网格参数
            mesh_data = config_manager.get_mesh_parameters()
            if mesh_data:
                success = self.mesh_manager.from_dict({"mesh_parameters": mesh_data})
                if success:
                    logger.info(f"成功加载 {len(mesh_data)} 个网格参数")
                else:
                    logger.warning("加载网格参数失败")

            # 设置当前网格
            current_mesh_id = config_manager.get_current_mesh_id()
            if current_mesh_id:
                self.mesh_manager.set_current_mesh(current_mesh_id)

            # 刷新UI显示
            self._refresh_all_ui()

        except Exception as e:
            logger.error(f"加载配置数据失败: {str(e)}", exc_info=True)
            QMessageBox.warning(self, "警告", f"加载配置数据失败: {str(e)}")

    def setup_animated_buttons(self):
        """为窗口中的按钮添加动画效果"""
        buttons = [
            # 网格管理按钮
            self.ui.btn_add_mesh,
            self.ui.btn_import_mesh,
            self.ui.btn_export_mesh,

            # 批量操作按钮
            self.ui.btn_select_all_meshes,
            self.ui.btn_select_none_meshes,
            self.ui.btn_select_inverse_meshes,
            self.ui.btn_apply_filter,
            self.ui.btn_batch_generate,
            self.ui.btn_stop_generation,

            # 模态分析按钮
            self.ui.btn_single_modal,
            self.ui.btn_start_modal_calculation,
            self.ui.btn_use_selected_meshes,
            self.ui.btn_use_recommended_mesh,
            self.ui.btn_select_all_for_modal,

            # 批量计算按钮
            self.ui.btn_batch_calculate,
            self.ui.btn_pause_calculation,
            self.ui.btn_stop_calculation,

            # 最优推荐按钮
            self.ui.btn_analyze_convergence,
            self.ui.btn_recommend_mesh,
            self.ui.btn_confirm_recommendation,

            # 结果导出按钮
            self.ui.btn_export_results,

            # 底部导航按钮
            self.ui.btn_generate_mesh,
            self.ui.btn_view_results,
            self.ui.btn_previous,
            self.ui.btn_next,
            self.ui.btn_main_menu
        ]

        # 应用动画效果
        self.apply_animated_buttons(buttons)

        logger.debug("按钮动画效果设置完成")

    # ==================== 网格管理事件处理 ====================

    def _on_add_mesh(self):
        """添加网格按钮点击处理"""
        try:
            dialog = MeshParameterDialog(self)
            dialog.parameter_accepted.connect(self._add_new_mesh)
            dialog.exec()

        except Exception as e:
            logger.error(f"添加网格对话框失败: {str(e)}", exc_info=True)
            QMessageBox.critical(self, "错误", f"添加网格对话框失败: {str(e)}")

    def _add_new_mesh(self, mesh_parameter: MeshParameter):
        """添加新网格参数"""
        try:
            success = self.mesh_manager.add_mesh(mesh_parameter)
            if success:
                # 保存配置
                self._save_configuration()

                # 强制刷新所有UI组件（确保信号处理完成后的额外刷新）
                self._refresh_all_ui()

                # 处理事件循环，确保UI更新
                QApplication.processEvents()

                # 显示成功消息
                mesh_count = self.mesh_manager.mesh_count
                QMessageBox.information(
                    self, "添加成功",
                    f"成功添加网格: {mesh_parameter.name}\n当前共 {mesh_count} 个网格"
                )

                logger.info(f"成功添加网格: {mesh_parameter.name}, 总网格数: {mesh_count}")

                # 显示状态消息
                self.show_status_message(f"成功添加网格 '{mesh_parameter.name}'")

            else:
                QMessageBox.warning(self, "添加失败", "添加网格失败，请检查参数设置")

        except Exception as e:
            logger.error(f"添加网格失败: {str(e)}", exc_info=True)
            QMessageBox.critical(self, "错误", f"添加网格失败: {str(e)}")

    def _on_import_mesh(self):
        """导入网格配置按钮点击处理"""
        try:
            file_path, _ = QFileDialog.getOpenFileName(
                self, "导入网格配置", "", "JSON文件 (*.json);;所有文件 (*)"
            )

            if file_path:
                # 记录导入前的网格数量
                initial_count = self.mesh_manager.mesh_count

                success = self.mesh_manager.import_from_json(file_path)
                if success:
                    # 立即刷新所有UI组件
                    self._refresh_all_ui()

                    # 保存配置
                    self._save_configuration()

                    # 显示导入结果
                    final_count = self.mesh_manager.mesh_count
                    imported_count = final_count - initial_count if final_count > initial_count else final_count

                    QMessageBox.information(
                        self, "导入成功",
                        f"成功导入 {imported_count} 个网格配置\n总网格数量: {final_count}"
                    )

                    logger.info(f"成功导入网格配置: {file_path}, 导入数量: {imported_count}")

                    # 显示状态消息
                    self.show_status_message(f"成功导入 {imported_count} 个网格配置")

                else:
                    QMessageBox.warning(self, "导入失败", "网格配置导入失败，请检查文件格式")

        except Exception as e:
            logger.error(f"导入网格配置失败: {str(e)}", exc_info=True)
            QMessageBox.critical(self, "错误", f"导入网格配置失败: {str(e)}")

    def _on_export_mesh(self):
        """导出网格配置按钮点击处理"""
        try:
            if self.mesh_manager.mesh_count == 0:
                QMessageBox.information(self, "提示", "没有网格参数可以导出")
                return

            file_path, _ = QFileDialog.getSaveFileName(
                self, "导出网格配置", "mesh_config.json", "JSON文件 (*.json);;所有文件 (*)"
            )

            if file_path:
                success = self.mesh_manager.export_to_json(file_path)
                if success:
                    QMessageBox.information(self, "成功", "网格配置导出成功")
                    logger.info(f"成功导出网格配置: {file_path}")
                else:
                    QMessageBox.warning(self, "失败", "网格配置导出失败")
        except Exception as e:
            logger.error(f"导出网格配置失败: {str(e)}", exc_info=True)
            QMessageBox.critical(self, "错误", f"导出网格配置失败: {str(e)}")

    def _on_mesh_selection_changed(self):
        """网格表格选择变化处理"""
        try:
            selected_items = self.ui.tableWidget_mesh_params.selectedItems()
            if selected_items:
                row = selected_items[0].row()
                mesh_id = self.ui.tableWidget_mesh_params.item(row, 0).data(Qt.ItemDataRole.UserRole)
                if mesh_id:
                    self.mesh_manager.set_current_mesh(mesh_id)
                    logger.debug(f"选择网格: {mesh_id}")

        except Exception as e:
            logger.error(f"处理网格选择变化失败: {str(e)}", exc_info=True)

    def _on_mesh_double_clicked(self, row: int, column: int):
        """网格表格双击处理"""
        try:
            mesh_id = self.ui.tableWidget_mesh_params.item(row, 0).data(Qt.ItemDataRole.UserRole)
            if mesh_id:
                mesh_param = self.mesh_manager.get_mesh_by_id(mesh_id)
                if mesh_param:
                    # 打开编辑对话框
                    dialog = MeshParameterDialog(self, mesh_param)
                    dialog.parameter_accepted.connect(lambda updated_param: self._update_mesh(mesh_id, updated_param))
                    dialog.exec()

        except Exception as e:
            logger.error(f"处理网格双击失败: {str(e)}", exc_info=True)

    def _update_mesh(self, mesh_id: str, updated_param: MeshParameter):
        """更新网格参数"""
        try:
            success = self.mesh_manager.update_mesh(mesh_id, updated_param)
            if success:
                self._save_configuration()
                QMessageBox.information(self, "更新成功", f"网格 '{updated_param.name}' 更新成功")
                logger.info(f"网格更新成功: {updated_param.name}")
            else:
                QMessageBox.warning(self, "更新失败", "网格更新失败")

        except Exception as e:
            logger.error(f"更新网格失败: {str(e)}", exc_info=True)
            QMessageBox.critical(self, "错误", f"更新网格失败: {str(e)}")

    def _on_preview_mesh_changed(self, index: int):
        """网格预览选择变化处理"""
        try:
            combo = self.ui.comboBox_mesh_select
            mesh_id = combo.itemData(index)

            if mesh_id:
                mesh_param = self.mesh_manager.get_mesh_by_id(mesh_id)
                if mesh_param:
                    # 更新网格信息显示
                    info_text = f"""
网格名称: {mesh_param.name}
网格尺寸: {mesh_param.size} mm
单元类型: {mesh_param.element_type.value}
状态: {mesh_param.status.value}
创建时间: {mesh_param.created_time.strftime('%Y-%m-%d %H:%M:%S')}

统计信息:
- 节点数: {mesh_param.statistics.node_count}
- 单元数: {mesh_param.statistics.element_count}
- 平均质量: {mesh_param.statistics.avg_quality:.3f}
- 生成时间: {mesh_param.statistics.generation_time:.1f}s

模态结果:
- 频率数量: {len(mesh_param.modal_results.frequencies)}
- 计算时间: {mesh_param.modal_results.calculation_time:.1f}s
                    """
                    self.ui.textEdit_mesh_info.setPlainText(info_text.strip())

                    # 设置为当前网格
                    self.mesh_manager.set_current_mesh(mesh_id)
            else:
                self.ui.textEdit_mesh_info.setPlainText("请选择一个网格以查看详细信息")

        except Exception as e:
            logger.error(f"处理网格预览变化失败: {str(e)}", exc_info=True)

    # ==================== 批量操作事件处理 ====================

    def _on_select_all_meshes(self):
        """全选网格按钮点击处理"""
        try:
            # 占位符实现
            QMessageBox.information(self, "功能提示", "全选网格功能")
            logger.info("全选网格功能被调用")
        except Exception as e:
            logger.error(f"全选网格失败: {str(e)}", exc_info=True)

    def _on_select_none_meshes(self):
        """全不选网格按钮点击处理"""
        try:
            # 占位符实现
            QMessageBox.information(self, "功能提示", "全不选网格功能")
            logger.info("全不选网格功能被调用")
        except Exception as e:
            logger.error(f"全不选网格失败: {str(e)}", exc_info=True)

    def _on_select_inverse_meshes(self):
        """反选网格按钮点击处理"""
        try:
            # 占位符实现
            QMessageBox.information(self, "功能提示", "反选网格功能")
            logger.info("反选网格功能被调用")
        except Exception as e:
            logger.error(f"反选网格失败: {str(e)}", exc_info=True)

    def _on_apply_filter(self):
        """应用筛选按钮点击处理"""
        try:
            # 占位符实现
            QMessageBox.information(self, "功能提示", "应用筛选功能")
            logger.info("应用筛选功能被调用")
        except Exception as e:
            logger.error(f"应用筛选失败: {str(e)}", exc_info=True)

    def _on_batch_generate(self):
        """批量生成按钮点击处理"""
        try:
            # 占位符实现
            QMessageBox.information(self, "功能提示", "批量生成网格功能")
            logger.info("批量生成网格功能被调用")
        except Exception as e:
            logger.error(f"批量生成失败: {str(e)}", exc_info=True)

    def _on_stop_generation(self):
        """停止生成按钮点击处理"""
        try:
            # 占位符实现
            QMessageBox.information(self, "功能提示", "停止生成功能")
            logger.info("停止生成功能被调用")
        except Exception as e:
            logger.error(f"停止生成失败: {str(e)}", exc_info=True)

    # ==================== 模态分析事件处理 ====================

    def _on_single_modal(self):
        """单个模态分析按钮点击处理"""
        try:
            current_mesh = self.mesh_manager.current_mesh
            if not current_mesh:
                QMessageBox.information(self, "提示", "请先选择要分析的网格")
                return

            QMessageBox.information(self, "模态分析", f"开始单个模态分析: {current_mesh.name}")
            logger.info(f"单个模态分析: {current_mesh.name}")
        except Exception as e:
            logger.error(f"单个模态分析失败: {str(e)}", exc_info=True)

    def _on_start_modal_calculation(self):
        """开始模态计算按钮点击处理"""
        try:
            # 占位符实现
            QMessageBox.information(self, "功能提示", "开始模态计算功能")
            logger.info("开始模态计算功能被调用")
        except Exception as e:
            logger.error(f"开始模态计算失败: {str(e)}", exc_info=True)

    def _on_freq_limit_toggled(self, checked: bool):
        """频率限制复选框切换处理"""
        try:
            logger.debug(f"频率限制切换: {checked}")
            # 占位符实现 - 可以在这里启用/禁用频率范围控件
        except Exception as e:
            logger.error(f"频率限制切换失败: {str(e)}", exc_info=True)

    def _on_modal_params_changed(self):
        """模态参数变化处理"""
        try:
            logger.debug("模态参数发生变化")
            # 占位符实现 - 可以在这里验证参数有效性
        except Exception as e:
            logger.error(f"模态参数变化处理失败: {str(e)}", exc_info=True)

    def _on_use_selected_meshes(self):
        """使用已选择网格按钮点击处理"""
        try:
            # 占位符实现
            QMessageBox.information(self, "功能提示", "使用已选择网格功能")
            logger.info("使用已选择网格功能被调用")
        except Exception as e:
            logger.error(f"使用已选择网格失败: {str(e)}", exc_info=True)

    def _on_use_recommended_mesh(self):
        """使用推荐网格按钮点击处理"""
        try:
            # 占位符实现
            QMessageBox.information(self, "功能提示", "使用推荐网格功能")
            logger.info("使用推荐网格功能被调用")
        except Exception as e:
            logger.error(f"使用推荐网格失败: {str(e)}", exc_info=True)

    def _on_select_all_for_modal(self):
        """选择所有网格进行模态分析按钮点击处理"""
        try:
            # 占位符实现
            QMessageBox.information(self, "功能提示", "选择所有网格进行模态分析功能")
            logger.info("选择所有网格进行模态分析功能被调用")
        except Exception as e:
            logger.error(f"选择所有网格进行模态分析失败: {str(e)}", exc_info=True)

    # ==================== 批量计算事件处理 ====================

    def _on_batch_calculate(self):
        """批量计算按钮点击处理"""
        try:
            # 占位符实现
            QMessageBox.information(self, "功能提示", "批量计算功能")
            logger.info("批量计算功能被调用")
        except Exception as e:
            logger.error(f"批量计算失败: {str(e)}", exc_info=True)

    def _on_pause_calculation(self):
        """暂停计算按钮点击处理"""
        try:
            # 占位符实现
            QMessageBox.information(self, "功能提示", "暂停计算功能")
            logger.info("暂停计算功能被调用")
        except Exception as e:
            logger.error(f"暂停计算失败: {str(e)}", exc_info=True)

    def _on_stop_calculation(self):
        """停止计算按钮点击处理"""
        try:
            # 占位符实现
            QMessageBox.information(self, "功能提示", "停止计算功能")
            logger.info("停止计算功能被调用")
        except Exception as e:
            logger.error(f"停止计算失败: {str(e)}", exc_info=True)

    # ==================== 最优推荐事件处理 ====================

    def _on_analyze_convergence(self):
        """分析收敛性按钮点击处理"""
        try:
            # 占位符实现
            QMessageBox.information(self, "功能提示", "分析收敛性功能")
            logger.info("分析收敛性功能被调用")
        except Exception as e:
            logger.error(f"分析收敛性失败: {str(e)}", exc_info=True)

    def _on_recommend_mesh(self):
        """推荐网格按钮点击处理"""
        try:
            # 占位符实现
            QMessageBox.information(self, "功能提示", "推荐网格功能")
            logger.info("推荐网格功能被调用")
        except Exception as e:
            logger.error(f"推荐网格失败: {str(e)}", exc_info=True)

    def _on_confirm_recommendation(self):
        """确认推荐按钮点击处理"""
        try:
            # 占位符实现
            QMessageBox.information(self, "功能提示", "确认推荐功能")
            logger.info("确认推荐功能被调用")
        except Exception as e:
            logger.error(f"确认推荐失败: {str(e)}", exc_info=True)

    # ==================== 显示选项事件处理 ====================

    def _on_display_option_changed(self):
        """显示选项变化处理"""
        try:
            logger.debug("显示选项发生变化")
            # 占位符实现 - 可以在这里更新图表显示
        except Exception as e:
            logger.error(f"显示选项变化处理失败: {str(e)}", exc_info=True)

    # ==================== 标签页事件处理 ====================

    def _on_tab_changed(self, index: int):
        """标签页切换处理"""
        try:
            tab_names = ["网格管理", "网格生成", "模态分析", "结果对比"]
            if 0 <= index < len(tab_names):
                logger.debug(f"切换到标签页: {tab_names[index]}")
                # 占位符实现 - 可以在这里更新UI状态
        except Exception as e:
            logger.error(f"标签页切换处理失败: {str(e)}", exc_info=True)

    # ==================== 简化的操作事件处理 ====================

    def _on_generate_mesh_nav(self):
        """底部导航生成网格按钮点击处理"""
        try:
            # 切换到网格生成标签页
            self.ui.tabWidget_main.setCurrentIndex(1)
            logger.info("切换到网格生成标签页")
        except Exception as e:
            logger.error(f"切换到网格生成标签页失败: {str(e)}", exc_info=True)

    def _on_view_results(self):
        """查看结果按钮点击处理"""
        try:
            # 切换到结果对比标签页
            self.ui.tabWidget_main.setCurrentIndex(3)
            logger.info("切换到结果对比标签页")
        except Exception as e:
            logger.error(f"切换到结果对比标签页失败: {str(e)}", exc_info=True)

    def _on_generate_mesh(self):
        """生成网格按钮点击处理"""
        try:
            current_mesh = self.mesh_manager.current_mesh
            if not current_mesh:
                QMessageBox.information(self, "提示", "请先选择要生成的网格")
                return

            # 更新状态为生成中
            current_mesh.update_status(MeshStatus.GENERATING)

            # 这里应该调用实际的网格生成逻辑
            # 目前只是模拟生成过程
            QMessageBox.information(self, "网格生成", f"开始生成网格: {current_mesh.name}")

            # 模拟生成完成
            current_mesh.update_status(MeshStatus.GENERATED)
            current_mesh.statistics.node_count = 10000
            current_mesh.statistics.element_count = 8000
            current_mesh.statistics.avg_quality = 0.85
            current_mesh.statistics.generation_time = 120.0

            logger.info(f"网格生成完成: {current_mesh.name}")
            self._refresh_all_ui()

        except Exception as e:
            logger.error(f"网格生成失败: {str(e)}", exc_info=True)
            QMessageBox.critical(self, "错误", f"网格生成失败: {str(e)}")

    def _on_modal_analysis(self):
        """模态分析按钮点击处理"""
        try:
            current_mesh = self.mesh_manager.current_mesh
            if not current_mesh:
                QMessageBox.information(self, "提示", "请先选择要分析的网格")
                return

            if current_mesh.status != MeshStatus.GENERATED:
                QMessageBox.information(self, "提示", "请先生成网格后再进行模态分析")
                return

            # 更新状态为计算中
            current_mesh.update_status(MeshStatus.CALCULATING)

            # 这里应该调用实际的模态分析逻辑
            # 目前只是模拟分析过程
            QMessageBox.information(self, "模态分析", f"开始模态分析: {current_mesh.name}")

            # 模拟分析完成
            current_mesh.update_status(MeshStatus.COMPLETED)
            current_mesh.modal_results.frequencies = [10.5, 15.2, 22.8, 35.1, 48.7]
            current_mesh.modal_results.calculation_time = 180.0

            logger.info(f"模态分析完成: {current_mesh.name}")
            self._refresh_all_ui()

        except Exception as e:
            logger.error(f"模态分析失败: {str(e)}", exc_info=True)
            QMessageBox.critical(self, "错误", f"模态分析失败: {str(e)}")

    def _on_export_results(self):
        """导出结果按钮点击处理"""
        try:
            completed_meshes = self.mesh_manager.get_meshes_by_status(MeshStatus.COMPLETED)
            if not completed_meshes:
                QMessageBox.information(self, "提示", "没有已完成的分析结果可以导出")
                return

            file_path, _ = QFileDialog.getSaveFileName(
                self, "导出分析结果", "analysis_results.json", "JSON文件 (*.json);;所有文件 (*)"
            )

            if file_path:
                # 构建导出数据
                export_data = {
                    "results": [],
                    "export_info": {
                        "count": len(completed_meshes),
                        "timestamp": datetime.now().isoformat(),
                        "source": "网格无关性验证系统"
                    }
                }

                for mesh in completed_meshes:
                    mesh_data = {
                        "name": mesh.name,
                        "size": mesh.size,
                        "element_type": mesh.element_type.value,
                        "frequencies": mesh.modal_results.frequencies,
                        "calculation_time": mesh.modal_results.calculation_time,
                        "statistics": {
                            "node_count": mesh.statistics.node_count,
                            "element_count": mesh.statistics.element_count,
                            "avg_quality": mesh.statistics.avg_quality
                        }
                    }
                    export_data["results"].append(mesh_data)

                # 保存文件
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(export_data, f, ensure_ascii=False, indent=2)

                QMessageBox.information(self, "成功", f"成功导出 {len(completed_meshes)} 个分析结果到:\n{file_path}")
                logger.info(f"成功导出分析结果到: {file_path}")

        except Exception as e:
            logger.error(f"导出分析结果失败: {str(e)}", exc_info=True)
            QMessageBox.critical(self, "错误", f"导出分析结果失败: {str(e)}")

    # ==================== 导航事件处理 ====================

    def _on_previous(self):
        """上一步按钮点击处理 - 跳转到约束设置"""
        try:
            from core.navigation_manager import navigate_to_previous_step
            from window_manager import WindowType
            success = navigate_to_previous_step(self.window_manager, WindowType.MESH)
            if success:
                logger.info("成功跳转到约束设置界面")
            else:
                logger.warning("跳转到约束设置失败")
                QMessageBox.warning(self, "跳转失败", "无法跳转到约束设置界面")
        except Exception as e:
            logger.error(f"跳转到约束设置失败: {str(e)}", exc_info=True)
            QMessageBox.warning(self, "跳转失败", f"无法跳转到约束设置界面: {str(e)}")

    def _on_next(self):
        """下一步按钮点击处理 - 跳转到计算结果"""
        try:
            from core.navigation_manager import navigate_to_next_step
            from window_manager import WindowType
            success = navigate_to_next_step(self.window_manager, WindowType.MESH)
            if success:
                logger.info("成功跳转到计算结果界面")
            else:
                logger.warning("跳转到计算结果失败")
                QMessageBox.warning(self, "跳转失败", "无法跳转到计算结果界面")
        except Exception as e:
            logger.error(f"跳转到计算结果失败: {str(e)}", exc_info=True)
            QMessageBox.warning(self, "跳转失败", f"无法跳转到计算结果界面: {str(e)}")

    def _on_main_menu(self):
        """主菜单按钮点击处理"""
        try:
            from ctrl.mesh_slot import to_main_slot
            to_main_slot(self.window_manager)
            logger.info("成功跳转到主菜单")
        except Exception as e:
            logger.error(f"跳转到主菜单失败: {str(e)}", exc_info=True)
            QMessageBox.warning(self, "跳转失败", f"无法跳转到主菜单: {str(e)}")

    # ==================== 网格管理器信号处理 ====================

    def _on_mesh_added(self, mesh_id: str):
        """网格添加信号处理"""
        try:
            self._refresh_all_ui()
            logger.debug(f"网格添加UI更新: {mesh_id}")
        except Exception as e:
            logger.error(f"处理网格添加信号失败: {str(e)}", exc_info=True)

    def _on_mesh_removed(self, mesh_id: str):
        """网格删除信号处理"""
        try:
            self._refresh_all_ui()
            logger.debug(f"网格删除UI更新: {mesh_id}")
        except Exception as e:
            logger.error(f"处理网格删除信号失败: {str(e)}", exc_info=True)

    def _on_mesh_updated(self, mesh_id: str):
        """网格更新信号处理"""
        try:
            self._refresh_all_ui()
            logger.debug(f"网格更新UI更新: {mesh_id}")
        except Exception as e:
            logger.error(f"处理网格更新信号失败: {str(e)}", exc_info=True)

    def _on_mesh_status_changed(self, mesh_id: str, old_status: MeshStatus, new_status: MeshStatus):
        """网格状态变化信号处理"""
        try:
            self._refresh_all_ui()
            logger.debug(f"网格状态变化: {mesh_id} {old_status.value} -> {new_status.value}")
        except Exception as e:
            logger.error(f"处理网格状态变化信号失败: {str(e)}", exc_info=True)

    def _on_current_mesh_changed(self, mesh_id: Optional[str]):
        """当前网格变更信号处理"""
        try:
            self._refresh_mesh_preview()
            self.update_ui_state()
            logger.debug(f"当前网格变更: {mesh_id}")
        except Exception as e:
            logger.error(f"处理当前网格变更信号失败: {str(e)}", exc_info=True)

    def _on_mesh_error(self, error_message: str):
        """网格错误信号处理"""
        try:
            QMessageBox.warning(self, "网格操作错误", error_message)
            logger.warning(f"网格操作错误: {error_message}")
        except Exception as e:
            logger.error(f"处理网格错误信号失败: {str(e)}", exc_info=True)

    # ==================== UI刷新方法 ====================

    def _refresh_all_ui(self):
        """刷新所有UI组件"""
        try:
            self._refresh_mesh_table()
            self._refresh_mesh_preview()
            self.update_ui_state()
            logger.debug("所有UI组件刷新完成")
        except Exception as e:
            logger.error(f"刷新所有UI组件失败: {str(e)}", exc_info=True)

    def _refresh_mesh_table(self):
        """刷新网格参数表格"""
        try:
            table = self.ui.tableWidget_mesh_params
            table.setRowCount(0)

            meshes = self.mesh_manager.get_all_meshes()
            for row, mesh in enumerate(meshes):
                table.insertRow(row)

                # 名称列
                name_item = QTableWidgetItem(mesh.name)
                name_item.setData(Qt.ItemDataRole.UserRole, mesh.id)
                table.setItem(row, 0, name_item)

                # 尺寸列
                size_item = QTableWidgetItem(f"{mesh.size:.2f}")
                table.setItem(row, 1, size_item)

                # 单元类型列
                type_item = QTableWidgetItem(mesh.element_type.value)
                table.setItem(row, 2, type_item)

                # 状态列
                status_item = QTableWidgetItem(mesh.status.value)
                # 根据状态设置颜色
                if mesh.status == MeshStatus.COMPLETED:
                    status_item.setBackground(QColor(200, 255, 200))
                elif mesh.status == MeshStatus.GENERATED:
                    status_item.setBackground(QColor(255, 255, 200))
                elif mesh.status == MeshStatus.GENERATING or mesh.status == MeshStatus.CALCULATING:
                    status_item.setBackground(QColor(255, 200, 200))
                table.setItem(row, 3, status_item)

                # 创建时间列
                time_item = QTableWidgetItem(mesh.created_time.strftime('%Y-%m-%d %H:%M'))
                table.setItem(row, 4, time_item)

                # 操作列
                operations_widget = QWidget()
                operations_layout = QHBoxLayout(operations_widget)
                operations_layout.setContentsMargins(2, 2, 2, 2)

                edit_btn = QPushButton("编辑")
                edit_btn.setMaximumSize(50, 25)
                edit_btn.clicked.connect(lambda checked, m_id=mesh.id: self._edit_mesh(m_id))

                delete_btn = QPushButton("删除")
                delete_btn.setMaximumSize(50, 25)
                delete_btn.clicked.connect(lambda checked, m_id=mesh.id: self._delete_mesh(m_id))

                operations_layout.addWidget(edit_btn)
                operations_layout.addWidget(delete_btn)
                table.setCellWidget(row, 5, operations_widget)

            logger.debug(f"网格参数表格刷新完成，共 {len(meshes)} 行")

        except Exception as e:
            logger.error(f"刷新网格参数表格失败: {str(e)}", exc_info=True)

    def _refresh_mesh_preview(self):
        """刷新网格预览下拉框"""
        try:
            combo = self.ui.comboBox_mesh_select
            combo.clear()
            combo.addItem("请选择网格", None)

            meshes = self.mesh_manager.get_all_meshes()
            for mesh in meshes:
                display_text = f"{mesh.name} ({mesh.size}mm, {mesh.status.value})"
                combo.addItem(display_text, mesh.id)

            # 设置当前选中的网格
            current_mesh = self.mesh_manager.current_mesh
            if current_mesh:
                for i in range(combo.count()):
                    if combo.itemData(i) == current_mesh.id:
                        combo.setCurrentIndex(i)
                        break

            logger.debug(f"网格预览下拉框刷新完成，共 {len(meshes)} 个网格")

        except Exception as e:
            logger.error(f"刷新网格预览下拉框失败: {str(e)}", exc_info=True)

    def update_ui_state(self):
        """更新UI状态"""
        try:
            has_meshes = self.mesh_manager.mesh_count > 0
            has_selection = self.mesh_manager.current_mesh is not None
            has_generated = len(self.mesh_manager.get_meshes_by_status(MeshStatus.GENERATED)) > 0
            has_completed = len(self.mesh_manager.get_meshes_by_status(MeshStatus.COMPLETED)) > 0

            # 更新按钮状态
            self.ui.btn_generate_mesh.setEnabled(has_selection)
            self.ui.btn_single_modal.setEnabled(has_selection and
                                               self.mesh_manager.current_mesh and
                                               self.mesh_manager.current_mesh.status == MeshStatus.GENERATED)
            self.ui.btn_start_modal_calculation.setEnabled(has_generated)
            self.ui.btn_export_results.setEnabled(has_completed)
            self.ui.btn_export_mesh.setEnabled(has_meshes)

            logger.debug(f"UI状态更新完成: 网格数={self.mesh_manager.mesh_count}, 选中={has_selection}")

        except Exception as e:
            logger.error(f"更新UI状态失败: {str(e)}", exc_info=True)

    # ==================== 辅助方法 ====================

    def _edit_mesh(self, mesh_id: str):
        """编辑网格"""
        try:
            mesh_param = self.mesh_manager.get_mesh_by_id(mesh_id)
            if mesh_param:
                dialog = MeshParameterDialog(self, mesh_param)
                dialog.parameter_accepted.connect(lambda updated_param: self._update_mesh(mesh_id, updated_param))
                dialog.exec()
        except Exception as e:
            logger.error(f"编辑网格失败: {str(e)}", exc_info=True)

    def _delete_mesh(self, mesh_id: str):
        """删除网格"""
        try:
            mesh_param = self.mesh_manager.get_mesh_by_id(mesh_id)
            if mesh_param:
                reply = QMessageBox.question(
                    self, "确认删除",
                    f"确定要删除网格 '{mesh_param.name}' 吗？",
                    QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
                )

                if reply == QMessageBox.StandardButton.Yes:
                    success = self.mesh_manager.remove_mesh(mesh_id)
                    if success:
                        self._save_configuration()
                        QMessageBox.information(self, "删除成功", f"网格 '{mesh_param.name}' 已删除")
                        logger.info(f"网格删除成功: {mesh_param.name}")
                    else:
                        QMessageBox.warning(self, "删除失败", "网格删除失败")
        except Exception as e:
            logger.error(f"删除网格失败: {str(e)}", exc_info=True)

    def _save_configuration(self):
        """保存配置"""
        try:
            from core.config_manager import ConfigManager
            config_manager = ConfigManager()

            # 保存网格参数
            mesh_data = {}
            for mesh in self.mesh_manager.get_all_meshes():
                mesh_data[mesh.id] = mesh.to_dict()

            config_manager.set_mesh_parameters(mesh_data)

            # 保存当前网格ID
            current_mesh = self.mesh_manager.current_mesh
            if current_mesh:
                config_manager.set_current_mesh_id(current_mesh.id)

            logger.debug("配置保存完成")

        except Exception as e:
            logger.error(f"保存配置失败: {str(e)}", exc_info=True)
